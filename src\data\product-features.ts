// 定义标注点的类型
export interface FeaturePoint {
    id: string;
    x: number; // 百分比位置 (0-100)
    y: number; // 百分比位置 (0-100)
    title: string;
    description: string;
}

// 产品特性数据配置
export interface ProductFeatureConfig {
    imageSrc: string;
    imageAlt: string;
    featurePoints: FeaturePoint[];
}

export const q1: ProductFeatureConfig = {
    imageSrc: "/image/mdt/QUASAR-Raw-Carbon-Non-Honeycomb-Core-Pickleball-Paddle-NeonTraIl-Meowzart.png",
    imageAlt: "RC QUASAR Raw Carbon Non Honeycomb Core Pickleball Paddle NeonTraIl Meowzart",
    featurePoints: [
        {
            id: "1",
            x: 25,
            y: 20,
            title: "Dynamic Weight Technology",
            description: "Strategically placed internal weights for enhanced sweet spot and stability"
        },
        {
            id: "2",
            x: 35,
            y: 15,
            title: "Metallic Poly Carbon Fiber Weave",
            description: "Ultimate pop and control for precise shots and responsive feel"
        },
        {
            id: "3",
            x: 45,
            y: 18,
            title: "Edgeless Design",
            description: "Lightweight, balanced paddle with faster hand speed"
        },
        {
            id: "4",
            x: 55,
            y: 22,
            title: "Advanced Core Technology",
            description: "Enhanced core for better power and control"
        },
        {
            id: "5",
            x: 65,
            y: 25,
            title: "Specialized Grip",
            description: "Enhanced grip for better control and reduced fatigue"
        },
        {
            id: "+",
            x: 75,
            y: 15,
            title: "Premium Materials",
            description: "High-quality materials for professional performance"
        }
    ]
};
export const novaAuroraFeatures: ProductFeatureConfig = {
    imageSrc: "/image/mdt/NOVA Carbon Fiber Pickleball Paddle- Aurora.png",
    imageAlt: "NOVA Carbon Fiber Pickleball Paddle Aurora Features",
    featurePoints: [
        {
            id: "1",
            x: 20,
            y: 25,
            title: "Carbon Fiber Surface",
            description: "Premium carbon fiber construction for superior durability and performance"
        },
        {
            id: "2",
            x: 40,
            y: 20,
            title: "Aurora Design",
            description: "Stunning aurora-inspired graphics with professional performance"
        },
        {
            id: "3",
            x: 60,
            y: 30,
            title: "Optimized Weight Distribution",
            description: "Perfectly balanced for enhanced control and power"
        },
        {
            id: "4",
            x: 50,
            y: 70,
            title: "Comfort Grip",
            description: "Ergonomic grip design for extended play comfort"
        }
    ]
};
export const axisFadeFeatures: ProductFeatureConfig = {
    imageSrc: "/image/mdt/AXIS Composite Pickleball Paddle-Fade.png",
    imageAlt: "AXIS Composite Pickleball Paddle Fade Features",
    featurePoints: [
        {
            id: "1",
            x: 30,
            y: 25,
            title: "Composite Surface",
            description: "High-performance composite material for optimal ball control"
        },
        {
            id: "2",
            x: 50,
            y: 20,
            title: "Fade Design",
            description: "Eye-catching fade pattern with professional aesthetics"
        },
        {
            id: "3",
            x: 70,
            y: 35,
            title: "Enhanced Sweet Spot",
            description: "Enlarged sweet spot for consistent performance"
        },
        {
            id: "4",
            x: 45,
            y: 75,
            title: "Professional Grip",
            description: "Tournament-grade grip for competitive play"
        }
    ]
};

// 产品特性数据映射
export const productFeaturesMap: Record<string, ProductFeatureConfig> = {
    'quasar-raw-carbon-non-honeycomb-core-pickleball-paddle-neontrail-meowzart': q1,
    'nova-aurora': novaAuroraFeatures,
    'axis-fade': axisFadeFeatures
};

// 默认产品特性（如果没有指定产品）
export const defaultProductFeatures = pulseVividFeatures;
