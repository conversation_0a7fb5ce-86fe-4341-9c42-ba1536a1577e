"use client"
import React, { useState, useRef } from "react";
import Image from "next/image";
import { Drawer } from "antd";
import { FeaturePoint, ProductFeatureConfig, defaultProductFeatures, productFeaturesMap } from "@/data/product-features";
import SEOOptimizedImage from "@/components/Image/SEOOptimizedImage";
import { useLocale, useTranslations } from "next-intl";
// 组件属性类型
interface ProductPaddleFeatureImgProps {
    productKey?: string;
    customConfig?: ProductFeatureConfig;
    className?: string;
}

export default function ProductPaddleFeatureImg({
    productKey,
    customConfig,
    className = ""
}: ProductPaddleFeatureImgProps) {
    const [activePoint, setActivePoint] = useState<string | null>(null);
    const [mobileDrawerOpen, setMobileDrawerOpen] = useState(false);
    const [selectedMobilePoint, setSelectedMobilePoint] = useState<FeaturePoint | null>(null);
    const imageRef = useRef<HTMLDivElement>(null);
	const t = useTranslations();
    // 获取产品配置
    const config = customConfig ||
                  (productKey ? productFeaturesMap[productKey] : null) ||
                  defaultProductFeatures;

    // 如果没有配置数据，不渲染组件
    if (!config) {
        return null;
    }

    // 计算弹窗位置的函数
    const getPopupPosition = (point: FeaturePoint) => {
        const cardWidth = 320; // 弹窗宽度
        const margin = 16; // 最小边距
        const spacing = 30; // 弹窗与标注点的间距

        // 计算左侧位置，增加间距
        const leftPosition = `calc(${point.x}% - ${cardWidth}px - ${spacing}px)`;

        // 如果点在屏幕左侧，则显示在右侧
        if (point.x < 40) {
            return {
                left: `calc(${point.x}% + ${spacing}px)`,
                top: `${point.y}%`,
                arrowPosition: 'left'
            };
        }

        // 默认显示在左侧
        return {
            left: `max(${margin}px, ${leftPosition})`,
            top: `${point.y}%`,
            arrowPosition: 'right'
        };
    };

    // 处理标注点点击
    const handlePointClick = (pointId: string) => {
        const point = config.featurePoints.find((p: FeaturePoint) => p.id === pointId);
        if (!point) return;

        // 检查是否为移动端（屏幕宽度小于768px）
        const isMobile = window.innerWidth < 768;

        if (isMobile) {
            // 移动端：打开抽屉
            setSelectedMobilePoint(point);
            setMobileDrawerOpen(true);
        } else {
            // 桌面端：切换弹窗
            setActivePoint(activePoint === pointId ? null : pointId);
        }
    };

    return (
        <div className={`relative ${className}`}>
            {/* 添加简单的呼吸动画 */}
            <style jsx>{`
                @keyframes simple-breathe {
                    0%, 100% {
                        transform: scale(1.2);
                        opacity: 0.6;
                    }
                    50% {
                        transform: scale(1.6);
                        opacity: 0.2;
                    }
                }
                .animate-simple-breathe {
                    animation: simple-breathe 4s ease-in-out infinite;
                }
            `}</style>

            {/* 图片容器 - 使用 h-auto 保持图片比例 */}
            <div ref={imageRef} className="relative w-full">
                <SEOOptimizedImage
                    src={config.imageSrc}
                    alt={config.imageAlt}
                    width={1920}
                    height={800}
                    className="w-full h-auto"
                    priority
                />

                {/* 标注点 - 使用响应式定位确保不偏移 */}
                {config.featurePoints.map((point: FeaturePoint) => (
                    <div
                        key={point.id}
                        className="absolute z-20"
                        style={{
                            left: `${point.x}%`,
                            top: `${point.y}%`,
                            transform: 'translate(-50%, -50%)', // 使用CSS transform确保居中
                        }}
                    >
                        {/* 外圈透明呼吸动画 - 一个简单的圆圈放大缩小 */}
                        <div className="absolute inset-0 rounded-full bg-white/60 animate-simple-breathe"></div>

                        {/* 标注点按钮 */}
                        <button
                            onClick={() => handlePointClick(point.id)}
                            className={`
                                relative w-[20px] h-[20px] md:w-[44px] md:h-[44px] rounded-full
                                bg-white
                                transition-all duration-300 ease-in-out
                                flex items-center justify-center z-10
                            `}
                            aria-label={`View feature: ${point.title}`}
                        >
                            {/* 黑色加号/减号 */}
                            <span className="text-gray-800  text-sm md:text-xl transition-transform duration-200">
                                {activePoint === point.id ? '−' : '+'}
                            </span>
                        </button>
                    </div>
                ))}

                {/* 特性详情卡片 - 智能定位 */}
                {activePoint && (() => {
                    const selectedPoint = config.featurePoints.find((p: FeaturePoint) => p.id === activePoint);
                    if (!selectedPoint) return null;

                    const position = getPopupPosition(selectedPoint);

                    return (
                        <div
                            className="absolute z-30 transform -translate-y-1/2"
                            style={{
                                left: position.left,
                                top: position.top
                            }}
                        >
                            <div className="bg-white/80 backdrop-blur-xl  p-6 w-80 max-w-xs transform transition-all duration-300 animate-in slide-in-from-left">
                                {/* 内容 */}
                                <div>
                                    {
                                        selectedPoint.title && <h3 className="text-lg font-bold text-gray-900 mb-2">
                                        {t(`productFeature.${selectedPoint.title}`)}
                                    </h3>
                                    }
                                    <p className="text-gray-600 text-sm leading-relaxed">
                                        {t(`productFeature.${selectedPoint.description}`)}
                                    </p>
                                </div>

                                {/* 指向标注点的箭头 - 根据位置调整方向 */}
                                {/* {position.arrowPosition === 'right' && (
                                    <div className="absolute right-0 top-1/2 transform translate-x-full -translate-y-1/2">
                                        <div className="w-0 h-0 border-l-8 border-l-white border-t-8 border-t-transparent border-b-8 border-b-transparent"></div>
                                    </div>
                                )}
                                {position.arrowPosition === 'left' && (
                                    <div className="absolute left-0 top-1/2 transform -translate-x-full -translate-y-1/2">
                                        <div className="w-0 h-0 border-r-8 border-r-white border-t-8 border-t-transparent border-b-8 border-b-transparent"></div>
                                    </div>
                                )} */}
                            </div>
                        </div>
                    );
                })()}

                {/* 点击遮罩关闭弹窗 - 仅桌面端 */}
                {activePoint && (
                    <div
                        className="absolute inset-0 z-20 hidden md:block"
                        onClick={() => setActivePoint(null)}
                    />
                )}
            </div>

            {/* 移动端抽屉 */}
            <Drawer
                title={selectedMobilePoint?.title}
                placement="bottom"
                onClose={() => setMobileDrawerOpen(false)}
                open={mobileDrawerOpen}
                height="auto"
                className="md:hidden"
                styles={{
                    body: { padding: '20px' },
                    header: {
                        borderBottom: '1px solid #f0f0f0',
                        fontSize: '18px',
                        fontWeight: 'bold'
                    }
                }}
            >
                {selectedMobilePoint && (
                    <div className="space-y-4">
                        <p className="text-gray-600 text-base leading-relaxed">
                            {selectedMobilePoint.description}
                        </p>
                    </div>
                )}
            </Drawer>
        </div>
    );
}