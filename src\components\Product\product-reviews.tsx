"use client";
import { useTranslations, useLocale } from "next-intl";
import React, { useState, useEffect, useCallback } from "react";
import axios from "axios";
import { App, Upload, But<PERSON>, Modal, Pagination } from "antd";
import type { UploadFile } from "antd";
import { PlusOutlined, UserOutlined } from "@ant-design/icons";
import Lightbox from "react-image-lightbox";
import "react-image-lightbox/style.css";
import SEOOptimizedImage from "@/components/Image/SEOOptimizedImage"
import { defaultLocale } from "@/config";
interface Review {
	id: string | number;
	comment_title: string;
	comment_content: string;
	comment_time: string;
	rating: number;
	first_name: string;
	last_name: string;
	image_urls?: string[];
}

interface FormDataType {
	comment_content: string;
	rating: number;
	client_first_name: string;
	client_last_name: string;
	client_email: string;
	comment_title: string;
}

interface ProductReviewsProps {
	productId: string;
	rating: number;
}


function ReviewForm({
	productId,
	onFormReset,
	onReviewAdded
}: {
	productId: string,
	onFormReset?: () => void,
	onReviewAdded?: () => void // 新增评论成功后的回调
}): JSX.Element {
	const { message } = App.useApp();
	const t = useTranslations("Review");
	const locale = useLocale();
	const initialFormData = {
		comment_title: "",
		comment_content: "",
		rating: 5,
		client_first_name: "",
		client_last_name: "",
		client_email: "",
	};

	const [formData, setFormData] = useState<FormDataType>(initialFormData);
	const [submitting, setSubmitting] = useState(false);
	const [fileList, setFileList] = useState<UploadFile[]>([]);
	const [errors, setErrors] = useState({
		comment_title: "",
		comment_content: "",
		client_first_name: "",
		client_last_name: "",
		client_email: "",
	});

	// 重置表单函数
	const resetForm = () => {
		setFormData(initialFormData);
		setFileList([]);
		setErrors({
			comment_title: "",
			comment_content: "",
			client_first_name: "",
			client_last_name: "",
			client_email: "",
		});
	};

	// 监听外部重置请求
	useEffect(() => {
		return () => {
			// 组件卸载时重置表单
			resetForm();
		};
	}, []);

	const validateForm = () => {
		let isValid = true;
		const newErrors = {
			comment_title: "",
			comment_content: "",
			client_first_name: "",
			client_last_name: "",
			client_email: "",
		};

		if (!formData.comment_title.trim()) {
			newErrors.comment_title = t("Title is required");
			isValid = false;
		}

		if (!formData.comment_content.trim()) {
			newErrors.comment_content = t("Review content is required");
			isValid = false;
		}

		if (!formData.client_first_name.trim()) {
			newErrors.client_first_name = t("First name is required");
			isValid = false;
		}

		if (!formData.client_last_name.trim()) {
			newErrors.client_last_name = t("Last name is required");
			isValid = false;
		}

		if (!formData.client_email.trim()) {
			newErrors.client_email = t("Email is required");
			isValid = false;
		} else if (!/\S+@\S+\.\S+/.test(formData.client_email)) {
			newErrors.client_email = t("Please enter a valid email");
			isValid = false;
		}

		setErrors(newErrors);
		return isValid;
	};

	const uploadImages = async (commentId: string) => {
		if (fileList.length === 0) return;

		const formData = new FormData();
		formData.append('comment_id', commentId);

		// 添加所有文件到 upload_list
		fileList.forEach((file, index) => {
			if (file.originFileObj) {
				formData.append(`upload_list`, file.originFileObj);
			}
		});

		try {
			await axios.post(
				`${process.env.NEXT_PUBLIC_LANGUAGE_LIST_URL}/saleor/comment_image_upload`,
				formData,
				{
					headers: {
						'Content-Type': 'multipart/form-data',
					},
				}
			);
		} catch (error) {
			console.error("Error uploading images:", error);
			message.error(t("Failed234"));
		}
	};

	const handleSubmit = async (e: React.FormEvent) => {
		e.preventDefault();
		if (!validateForm()) {
			return;
		}
		setSubmitting(true);
		try {
			const response = await axios.post(
				`${process.env.NEXT_PUBLIC_LANGUAGE_LIST_URL}/saleor/product_comment`,
				{
					...formData,
					product_id: {
						product_id: productId,
					},
				},
			);
			if (response.data.code === 200) {
				if (fileList.length > 0) {
					await uploadImages(response.data.detail.comment_id);
				}
				message.success(t("Review234"));
				resetForm();
				if (onFormReset) {
					onFormReset(); // 提交成功后关闭弹窗
				}
				if (onReviewAdded) {
					onReviewAdded(); // 通知父组件评论已添加，需要重新加载
				}
			}
		} catch (error) {
			message.error(t("Failed to submit review"));
		} finally {
			setSubmitting(false);
		}
	};

	const handleRatingChange = (newRating: number) => {
		setFormData((prev) => ({ ...prev, rating: newRating }));
	};

	const handleUploadChange = ({ fileList: newFileList }: { fileList: UploadFile[] }) => {
		setFileList(newFileList);
	};

	const beforeUpload = (file: File) => {
		const isImage = file.type.startsWith('image/');
		if (!isImage) {
			message.error(t('You23'));
		}
		const isLt5M = file.size / 1024 / 1024 < 5;
		if (!isLt5M) {
			message.error(t('Image21342'));
		}
		return isImage && isLt5M;
	};


	const uploadButton = (
		<div>
			<PlusOutlined />
			<div style={{ marginTop: 8 }}>{t("Upload")}</div>
		</div>
	);

	return (
		<div className="reviews  bg-white p-6  max-md:p-0">
			<h3 className={`${locale === defaultLocale ? "ib" : "font-semibold"} mb-4 text-4xl`}>{t("Write a Review")}</h3>
			<form onSubmit={handleSubmit} className="space-y-4">
				<div>
					<label className="mb-1 block text-xl font-medium text-black">{t("Rating")}</label>
					<div className="flex gap-1">
						{[1, 2, 3, 4, 5].map((star) => (
							<button
								key={star}
								type="button"
								onClick={() => handleRatingChange(star)}
								className="focus:outline-none"
							>
								<i
									className={`ri-star-${formData.rating >= star ? "fill" : "line"} text-2xl text-yellow-400`}
								></i>
							</button>
						))}
					</div>
				</div>

				<div>
					<label className="mb-1 block text-xl font-medium text-black">{t("Title345")}</label>
					<input
						type="text"
						required
						placeholder={t("Title345")}
						className={`w-full rounded border px-3 py-2 ${errors.comment_title ? 'border-red-500' : ''}`}
						value={formData.comment_title}
						onChange={(e) => {
							setFormData((prev) => ({ ...prev, comment_title: e.target.value }));
							if (errors.comment_title) setErrors((prev) => ({ ...prev, comment_title: '' }));
						}}
					/>
					{errors.comment_title && <p className="mt-1 text-sm text-red-500">{errors.comment_title}</p>}
				</div>

				<div>
					<label className="mb-1 block text-xl font-medium text-black">{t("Review")}</label>
					<textarea
						required
						placeholder={t("Review")}
						className={`h-24 w-full rounded border-[1px] ${errors.comment_content ? 'border-red-500' : 'border-black'} px-3 py-2`}
						value={formData.comment_content}
						onChange={(e) => {
							setFormData((prev) => ({ ...prev, comment_content: e.target.value }));
							if (errors.comment_content) setErrors((prev) => ({ ...prev, comment_content: '' }));
						}}
					></textarea>
					{errors.comment_content && <p className="mt-1 text-sm text-red-500">{errors.comment_content}</p>}
				</div>

				<div className="grid grid-cols-2 gap-4">
					<div>
						<label className="mb-1 block text-xl font-medium text-black">{t("First Name")}</label>
						<input
							type="text"
							required
							placeholder={t("First Name")}
							className={`w-full rounded border px-3 py-2 ${errors.client_first_name ? 'border-red-500' : ''}`}
							value={formData.client_first_name}
							onChange={(e) => {
								setFormData((prev) => ({ ...prev, client_first_name: e.target.value }));
								if (errors.client_first_name) setErrors((prev) => ({ ...prev, client_first_name: '' }));
							}}
						/>
						{errors.client_first_name && <p className="mt-1 text-sm text-red-500">{errors.client_first_name}</p>}
					</div>
					<div>
						<label className="mb-1 block text-xl font-medium text-black">{t("Last Name")}</label>
						<input
							type="text"
							required
							placeholder={t("Last Name")}
							className={`w-full rounded border px-3 py-2 ${errors.client_last_name ? 'border-red-500' : ''}`}
							value={formData.client_last_name}
							onChange={(e) => {
								setFormData((prev) => ({ ...prev, client_last_name: e.target.value }));
								if (errors.client_last_name) setErrors((prev) => ({ ...prev, client_last_name: '' }));
							}}
						/>
						{errors.client_last_name && <p className="mt-1 text-sm text-red-500">{errors.client_last_name}</p>}
					</div>
				</div>

				<div>
					<label className="mb-1 block text-xl font-medium text-black">{t("Email")}</label>
					<input
						type="email"
						required
						placeholder={t("Email")}
						className={`w-full rounded border px-3 py-2 ${errors.client_email ? 'border-red-500' : ''}`}
						value={formData.client_email}
						onChange={(e) => {
							setFormData((prev) => ({ ...prev, client_email: e.target.value }));
							if (errors.client_email) setErrors((prev) => ({ ...prev, client_email: '' }));
						}}
					/>
					{errors.client_email && <p className="mt-1 text-sm text-red-500">{errors.client_email}</p>}
				</div>

				{/* 图片上传部分 */}
				<div>
					<label className="mb-2 block text-xl font-medium text-black">
						{t("Upload Images")} ({t("Optional")})
					</label>
					<Upload
						listType="picture-card"
						fileList={fileList}
						onChange={handleUploadChange}
						beforeUpload={beforeUpload}
						maxCount={6}
						multiple
						accept="image/*"
						customRequest={({ onSuccess }) => {
							if (onSuccess) {
								onSuccess("ok");
							}
						}}
					>
						{fileList.length >= 6 ? null : uploadButton}
					</Upload>
				</div>

				<button
					type="submit"
					disabled={submitting}
					className="w-full rounded inline-block bg-main max-w-[300px] px-4 py-3	 text-white text-lg  disabled:bg-gray-300"
				>
					{submitting ? t("Submitting") : t("Submit Review")}
				</button>
			</form>
		</div>
	);
}


function ReviewCard({ review }: { review: Review }) {
	const locale = useLocale();
	const formattedDate = review.comment_time ? new Date(review.comment_time).toLocaleDateString() : '';
	const [isLightboxOpen, setIsLightboxOpen] = useState(false);
	const [photoIndex, setPhotoIndex] = useState(0);
	const hasImages = review.image_urls && review.image_urls.length > 0;
	return (
		<div className="py-6 border-b border-gray-200 last:border-b-0">
			{/* 星级评分 */}
			<div className="flex gap-1 mb-3">
				{[...Array(5)].map((_, i) => (
					<i key={i} className={`ri-star-${i < review.rating ? "fill" : "line"} text-yellow-400 text-lg`}></i>
				))}
			</div>

			{/* 用户信息和日期 */}
			<div className="flex items-center gap-2 mb-4">
				<div className="w-8 h-8 bg-[#efefef] flex items-center justify-center flex-shrink-0">
					<UserOutlined className="text-gray-600 text-xl" />
				</div>
				<span className={`text-base ${locale === defaultLocale ? "ib text-black " : "font-semibold text-black"}`}>
					{review?.first_name} {review?.last_name}
				</span>
				<span className={`text-sm text-gray-500`}>
					{formattedDate}
				</span>
			</div>

			{/* 评论标题 */}
			{review.comment_title && (
				<h3 className={`text-base ${locale === defaultLocale ? "ib" : "font-medium"} mb-2`}>
					{review.comment_title}
				</h3>
			)}

			{/* 评论内容 */}
			<p className="text-gray-700 text-sm leading-relaxed mb-6">
				{review.comment_content}
			</p>

			{/* 评论图片展示 */}
			{hasImages && (
				<div className="flex flex-wrap gap-3">
					{review.image_urls?.map((image, index) => (
						<div
							key={index}
							className="group relative cursor-pointer verflow-hidden"
							onClick={() => {
								setPhotoIndex(index);
								setIsLightboxOpen(true);
							}}
						>
							<SEOOptimizedImage
								src={image}
								width={800}
								height={800}
								alt={`Review image ${index + 1}`}
								className="h-16 w-16 md:h-28 md:w-28 object-cover transition-transform duration-200"
							/>
						</div>
					))}
				</div>
			)}

			{/* Lightbox 图片查看器 */}
			{isLightboxOpen && hasImages && review.image_urls && (
				<Lightbox
					mainSrc={review.image_urls[photoIndex]}
					nextSrc={review.image_urls[(photoIndex + 1) % review.image_urls.length]}
					prevSrc={review.image_urls[(photoIndex + review.image_urls.length - 1) % review.image_urls.length]}
					onCloseRequest={() => setIsLightboxOpen(false)}
					onMovePrevRequest={() => setPhotoIndex((photoIndex + review.image_urls.length - 1) % review.image_urls.length)}
					onMoveNextRequest={() => setPhotoIndex((photoIndex + 1) % review.image_urls.length)}
				/>
			)}
		</div>
	);
}

function ReviewList({
	reviews,
	productId,
	loading,
	currentPage,
	pageSize,
	total,
	onPageChange,
	onReviewAdded
}: {
	reviews: Review[],
	productId: string,
	loading: boolean,
	currentPage: number,
	pageSize: number,
	total: number,
	onPageChange: (page: number, pageSize: number) => void,
	onReviewAdded: () => void // 添加此回调
}) {
	const t = useTranslations("Review");
	const [isModalOpen, setIsModalOpen] = useState(false);
	const locale = useLocale();
	// 调试信息，可以在生产环境移除
	// console.log("ReviewList渲染:", { currentPage, pageSize, total, reviewsCount: reviews.length });

	const handleModalClose = () => {
		setIsModalOpen(false);
	};

	// 计算是否应该显示分页器
	const shouldShowPagination = total > pageSize;
	// console.log("显示分页器?", shouldShowPagination);

	return (
		<div className="space-y-4">
			<div className="mb-2 flex items-center justify-between border-b  border-gray-200 pb-4">
				<h2 className="text-base text-[#636363]">{t("Reviews")} {total}</h2>
			</div>

			{/* 评论列表 */}
			<div className="reviews space-y-0 relative min-h-[200px]">
				{loading ? (
					<div className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-70 z-10">
						<div className="h-8 w-8 animate-spin rounded-full border-b-2 border-gray-900"></div>
					</div>
				) : null}

				{reviews.map((review, index) => (
					<ReviewCard key={index} review={review} />
				))}

				{!loading && reviews.length === 0 && (
					<div className="py-10 text-center text-gray-500">
						{t("No reviews found for this page.")}
					</div>
				)}
			</div>

			{/* 分页组件 */}
			{total > pageSize && (
				<div className="flex justify-center mt-6">
					<Pagination
						current={currentPage}
						pageSize={pageSize}
						total={total}
						onChange={onPageChange}
						hideOnSinglePage={false}
						showSizeChanger={false}
						className="custom-pagination"
						itemRender={(page, type, originalElement) => {
							if (type === 'prev') {
								return <button className="pagination-nav-button"><i className="ri-arrow-left-s-line"></i></button>;
							}
							if (type === 'next') {
								return <button className="pagination-nav-button"><i className="ri-arrow-right-s-line"></i></button>;
							}
							return originalElement;
						}}
					/>
				</div>
			)}

			{/* 评论表单弹窗 */}
			<Modal
				title={null}
				open={isModalOpen}
				onCancel={handleModalClose}
				footer={null}
				width={800}
				centered
				destroyOnClose={true}
			>
				<ReviewForm
					productId={productId}
					onFormReset={() => handleModalClose()}
					onReviewAdded={onReviewAdded} // 传递回调
				/>
			</Modal>
		</div>
	);
}

export default function ProductReviews({ productId, rating }: ProductReviewsProps) {
	const [reviews, setReviews] = useState<Review[]>([]);
	const [loading, setLoading] = useState(true);
	const t = useTranslations("Review");
	const locale = useLocale();
	const [modalVisible, setModalVisible] = useState(false);

	// 添加分页状态
	const [currentPage, setCurrentPage] = useState(1);
	const [pageSize, setPageSize] = useState(5);
	const [total, setTotal] = useState(0);

	const handleMainModalClose = () => {
		setModalVisible(false);
	};

	// 新增整体统计计算
	const [overallRating, setOverallRating] = useState(0);
	const [totalReviews, setTotalReviews] = useState(0);
	const [recommendPercentage, setRecommendPercentage] = useState(0);
	const [starCounts, setStarCounts] = useState<number[]>([0, 0, 0, 0, 0]);
	const [recommends, setRecommends] = useState(0);

	// 使用useCallback包装fetchReviews函数以避免不必要的重新创建
	const fetchReviews = useCallback(async () => {
		setLoading(true);
		try {
			const { data } = await axios.get(
				`${process.env.NEXT_PUBLIC_LANGUAGE_LIST_URL}/saleor/product_comment`,
				{
					params: {
						product_id: productId,
						limit: pageSize,
						page: currentPage,
					},
				},
			);
			if (data.code == 200) {
				setReviews(data.detail.ret);

				// 获取准确的总数
				if (data.detail.total !== undefined) {
					// 如果API直接返回了总数
					setTotal(data.detail.total);
					console.log("API返回的总数:", data.detail.total);
				} else if (currentPage === 1) {
					// 如果API没有返回总数，而且是第一页，就用当前页的评论数
					setTotal(data.detail.ret.length);
					console.log("使用当前页评论数作为总数:", data.detail.ret.length);
				}
				// 如果不是第一页，保留之前的total

				// 如果在首页，更新评级统计
				if (currentPage === 1) {
					const reviewsForStats = data.detail.ret;
					const totalCount = reviewsForStats.length;
					const sum = reviewsForStats.reduce((acc: number, rev: Review) => acc + rev.rating, 0);
					const avg = totalCount > 0 ? (sum / totalCount).toFixed(1) : 0;
					const recommends = reviewsForStats.filter((rev: Review) => rev.rating >= 4).length;
					const percent = totalCount > 0 ? Math.round((recommends / totalCount) * 100) : 0;
					setOverallRating(Number(avg));
					setTotalReviews(totalCount);
					setRecommendPercentage(percent);
					setRecommends(recommends);
					const counts = [0, 0, 0, 0, 0];
					reviewsForStats.forEach((rev: Review) => {
						if (rev.rating >= 1 && rev.rating <= 5) counts[5 - rev.rating]++;
					});
					setStarCounts(counts);
				}
			}
		} catch (error) {
			console.error("Error fetching reviews:", error);
		} finally {
			setLoading(false);
		}
	}, [productId, currentPage, pageSize]);

	useEffect(() => {
		fetchReviews();
	}, [fetchReviews]);

	// 处理页码变化
	const handlePageChange = (page: number, pageSize: number) => {
		setCurrentPage(page);
		setPageSize(pageSize);
	};

	// 提交新评论后重新加载评论
	const handleReviewAdded = useCallback(() => {
		// 重置到第一页并重新加载数据
		setCurrentPage(1);
		// fetchReviews会通过依赖项自动触发
	}, []);

	// 在组件外部添加全局样式 - 可以放在一个单独的CSS文件中导入
	// 这里示范如何直接在组件中添加样式
	useEffect(() => {
		// 添加自定义样式
		const style = document.createElement('style');
		style.innerHTML = `
			.custom-pagination .ant-pagination-item {
				border-radius: 0;
				border: 1px solid #e5e7eb;
				min-width: 32px;
				height: 32px;
				line-height: 32px;
			}
			.custom-pagination .ant-pagination-item-active {
				background-color: #000;
				border-color: #000;
			}
			.custom-pagination .ant-pagination-item-active a {
				color: white !important;
			}
			.pagination-nav-button {
				display: flex;
				align-items: center;
				justify-content: center;
				border: 1px solid #e5e7eb;
				width: 32px;
				height: 32px;
				cursor: pointer;
			}
			.pagination-nav-button:hover {
				border-color: #000;
				color: #000;
			}
		`;
		document.head.appendChild(style);

		// 组件卸载时移除样式
		return () => {
			document.head.removeChild(style);
		};
	}, []);

	return (
		<div className="flex flex-col py-6 w-full px-4 md:px-6">
			{/* 整体评级显示 - 完全按照设计图 */}
			<div className="mb-4 w-full">
				<div className="flex border-t border-b border-gray-200 py-8 flex-col lg:flex-row justify-between gap-8 lg:gap-12">
					{/* 左侧：OVERALL RATING */}
					<div className="flex flex-col lg:min-h-[140px]">
						<h3 className="text-xl font-bold text-black uppercase tracking-wider mb-3">
							{t("Overall Rating")}
						</h3>
						<div className="flex-1 flex flex-col justify-center">
							<div className="flex items-end gap-3 mb-2">
								<div>
									<span className="text-5xl font-bold text-gray-900 leading-none">
										{rating.toFixed(1)}
									</span>
									<span className="text-base text-gray-600"> / 5</span>
								</div>

								<div className="flex gap-1">
									{[1, 2, 3, 4, 5].map(i => (
										<i
											key={i}
											className={`ri-star-${i <= rating ? 'fill' : 'line'} text-yellow-400 text-3xl`}
										></i>
									))}
								</div>
							</div>
							<p className="text-sm text-gray-600">
								{t("Based on")} {totalReviews} {t("reviews")}
							</p>
						</div>
					</div>

					{/* 中间：RATING SNAPSHOT */}
					<div className="flex-1 max-w-[300px]">
						<h3 className="text-xl font-bold text-black uppercase tracking-wider mb-3">
							{t("Rating Snapshot")}
						</h3>
						<div className="space-y-1">
							{[5, 4, 3, 2, 1].map((star) => {
								const count = starCounts[5 - star] || 0;
								const percentage = totalReviews > 0 ? Math.round((count / totalReviews) * 100) : 0;

								return (
									<div key={star} className="flex items-center gap-3">
										<div className="flex items-center gap-1 w-6">
											<p className={`text-lg text-black ${locale === defaultLocale ? "ib" : "font-semibold"} min-w-[10px] c-flex`}>{star}</p>
											<i className="ri-star-fill text-black text-xs"></i>
										</div>
										<div className="flex-1 h-2 bg-[#e5e5e5] rounded-full overflow-hidden">
											<div
												className={`h-full rounded-full transition-all duration-300 bg-yellow-400`}
												style={{ width: `${percentage}%` }}
											></div>
										</div>
										<span className="text-sm text-gray-700 w-8 text-right">
											{percentage}%
										</span>
									</div>
								);
							})}
						</div>
					</div>

					{/* 右侧：Write A Review 按钮 */}
					<div className="flex-shrink-0 flex flex-col lg:min-h-[140px]">
						<h3 className="text-xl font-bold text-black uppercase tracking-wider mb-3 opacity-0 pointer-events-none">
							{/* {t("Placeholder")} */}
						</h3>
						<div className="flex-1 flex items-center justify-center">
							<button
								onClick={() => setModalVisible(true)}
								className="bg-[#83c000] hover:bg-opacity-80 text-white px-10 py-2 rounded-full font-medium text-sm transition-colors duration-200 flex items-center justify-center gap-1"
							>
								<i className="ri-edit-line text-lg"></i>
								<span className="ml-2">{t("Write A Review")}</span>
							</button>
						</div>
					</div>
				</div>
			</div>

			{/* 评论列表或无评论提示 */}
			<div className="space-y-6">
				{!reviews.length && !loading ? (
					<div className="flex flex-col items-center justify-center rounded-lg bg-gray-50 py-12">
						<div className="mb-6 flex h-20 w-20 items-center justify-center rounded-full bg-gray-100">
							<i className="ri-emotion-sad-line text-4xl text-gray-500"></i>
						</div>
						<h2 className="mb-3 text-xl font-semibold text-gray-700">{t("No Reviews Yet")}</h2>
						<p className="max-w-md text-center text-sm text-gray-600">
							{t("Be9988")}
						</p>
						<Button
							type="primary"
							onClick={() => setModalVisible(true)}
							className="mt-4 bg-black hover:bg-gray-800 text-white"
						>
							{t("Write a Review")}
						</Button>
					</div>
				) : (
					<ReviewList
						reviews={reviews}
						productId={productId}
						loading={loading}
						currentPage={currentPage}
						pageSize={pageSize}
						total={total}
						onPageChange={handlePageChange}
						onReviewAdded={handleReviewAdded}
					/>
				)}
			</div>

			{/* 评论表单弹窗（用于"No Reviews Yet"情况） */}
			<Modal
				title={null}
				open={modalVisible}
				onCancel={handleMainModalClose}
				footer={null}
				width={800}
				centered
				destroyOnClose={true}
			>
				<ReviewForm
					productId={productId}
					onFormReset={() => handleMainModalClose()}
					onReviewAdded={handleReviewAdded} // 传递回调
				/>
			</Modal>
		</div>
	);
}
