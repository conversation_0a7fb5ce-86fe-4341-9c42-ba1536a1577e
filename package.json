{"name": "sh-hnzw-b", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": " next dev -p 3662", "build": "next build", "loadScript": "node script/index.js", "getTranslationList": "node script/getTanslationList.js", "predev": "yarn run generate", "start": "next start -p 3662", "start:pm2": "pm2 start npm  --name sh-hnzw-b -- start -- -p 3662", "restart:pm2": "pm2 restart sh-hnzw-b", "prebuild": "yarn run generate", "lint": "next lint --dir src --fix", "generate": "graphql-codegen --config .graphqlrc.ts", "prepare": "husky install", "test": "playwright test"}, "dependencies": {"@ant-design/nextjs-registry": "1.0.1", "@headlessui/react": "2.2.0", "@heroicons/react": "2.2.0", "@phosphor-icons/react": "2.1.7", "@react-three/drei": "9.111.3", "@react-three/fiber": "8.17.6", "@saleor/auth-sdk": "1.0.1", "@stripe/react-stripe-js": "2.6.2", "@stripe/stripe-js": "2.2.0", "@tailwindcss/container-queries": "0.1.1", "airwallex-payment-elements": "1.62.0", "animate.css": "4.1.1", "antd": "5.20.2", "axios": "1.7.8", "babel-runtime": "6.26.0", "class-variance-authority": "^0.7.0", "clsx": "2.1.0", "cookies-next": "4.2.1", "country-state-city": "3.2.1", "editorjs-html": "3.4.3", "formik": "2.4.5", "framer-motion": "11.12.0", "gsap": "3.12.5", "libphonenumber-js": "1.11.17", "lodash-es": "4.17.21", "lucide-react": "0.358.0", "md5": "2.3.0", "moment": "2.30.1", "next": "14.2.10", "next-intl": "latest", "node-fetch": "3.3.2", "occt-import-js": "0.0.22", "path": "0.12.7", "query-string": "8.1.0", "react": "^18.3.1", "react-countup": "6.5.3", "react-dom": "18.3.1", "react-error-boundary": "4.0.13", "react-fast-marquee": "1.6.5", "react-hook-form": "7.53.2", "react-image-lightbox": "5.1.4", "react-loading": "2.0.3", "react-loading-skeleton": "3.5.0", "react-markdown": "9.0.3", "react-masonry-css": "1.0.16", "react-paginate": "8.2.0", "react-phone-input-2": "2.15.1", "react-rating": "2.0.5", "react-scroll": "1.9.0", "react-share": "5.1.0", "react-spinners": "0.13.8", "react-toastify": "10.0.6", "react-visibility-sensor": "5.1.1", "rehype-raw": "7.0.0", "remixicon": "^3.6.0", "sass": "1.77.8", "scrollama": "3.2.0", "sharp": "0.33.5", "styled-components": "6.1.13", "swiper": "11.1.14", "tailwind-merge": "2.5.4", "three": "0.167.1", "ts-invariant": "0.10.3", "workerize": "0.1.8", "yup": "1.4.0", "zustand": "4.4.6", "nextjs-toploader": "3.8.16"}, "devDependencies": {"@graphql-codegen/cli": "5.0.0", "@graphql-codegen/client-preset": "4.1.0", "@graphql-typed-document-node/core": "3.2.0", "@next/env": "14.0.4", "@parcel/watcher": "2.3.0", "@playwright/test": "1.40.1", "@tailwindcss/forms": "0.5.7", "@tailwindcss/typography": "0.5.10", "@types/lodash-es": "4.17.12", "@types/md5": "2.3.5", "@types/node": "20.10.0", "@types/react": "18.2.38", "@types/react-dom": "18.2.17", "@types/three": "0.167.2", "@types/url-join": "4.0.3", "@typescript-eslint/eslint-plugin": "6.14.0", "@typescript-eslint/parser": "6.14.0", "autoprefixer": "10.4.16", "eslint": "8.56.0", "eslint-config-next": "14.0.4", "eslint-config-prettier": "9.1.0", "eslint-plugin-import": "2.29.1", "eslint-plugin-playwright": "0.18.0", "graphql-tag": "2.12.6", "husky": "8.0.3", "lint-staged": "15.1.0", "postcss": "8.4.32", "prettier": "3.1.1", "prettier-plugin-tailwindcss": "0.5.9", "schema-dts": "1.1.2", "tailwindcss": "3.4.0", "ts-node": "10.9.2", "typescript": "5.3.3", "wonka": "6.3.4"}, "resolutions": {"graphql": "16.8.1"}, "packageManager": "yarn@1.22.22", "engines": {"yarn": ">=1.22.19", "node": ">=18.17.0"}}